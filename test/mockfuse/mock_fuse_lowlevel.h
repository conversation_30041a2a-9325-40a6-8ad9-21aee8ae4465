// mock_fuse_lowlevel.h
#ifndef MOCK_FUSE_LOWLEVEL_H
#define MOCK_FUSE_LOWLEVEL_H

#include <pthread.h>
#include <stdint.h>
#include <sys/stat.h>
#include <sys/types.h>

#define FUSE_USE_VERSION 31

// Make sure we have the file type constants
#ifndef S_IFDIR
#define S_IFDIR 0040000
#endif

#ifndef S_IFREG
#define S_IFREG 0100000
#endif

// Forward declarations
typedef struct fuse_req *fuse_req_t;
typedef uint64_t fuse_ino_t;

// FUSE capabilities
#define FUSE_CAP_ASYNC_READ         (1 << 0)
#define FUSE_CAP_POSIX_LOCKS        (1 << 1)
#define FUSE_CAP_ATOMIC_O_TRUNC     (1 << 3)
#define FUSE_CAP_EXPORT_SUPPORT     (1 << 4)
#define FUSE_CAP_DONT_MASK          (1 << 6)
#define FUSE_CAP_SPLICE_WRITE       (1 << 7)
#define FUSE_CAP_SPLICE_MOVE        (1 << 8)
#define FUSE_CAP_SPLICE_READ        (1 << 9)
#define FUSE_CAP_FLOCK_LOCKS        (1 << 10)
#define FUSE_CAP_IOCTL_DIR          (1 << 11)
#define FUSE_CAP_AUTO_INVAL_DATA    (1 << 12)
#define FUSE_CAP_READDIRPLUS        (1 << 13)
#define FUSE_CAP_READDIRPLUS_AUTO   (1 << 14)
#define FUSE_CAP_ASYNC_DIO          (1 << 15)
#define FUSE_CAP_WRITEBACK_CACHE    (1 << 16)
#define FUSE_CAP_NO_OPEN_SUPPORT    (1 << 17)
#define FUSE_CAP_PARALLEL_DIROPS    (1 << 18)
#define FUSE_CAP_POSIX_ACL          (1 << 19)
#define FUSE_CAP_HANDLE_KILLPRIV    (1 << 20)
#define FUSE_CAP_CACHE_SYMLINKS     (1 << 23)
#define FUSE_CAP_NO_OPENDIR_SUPPORT (1 << 24)

// File info structure
struct fuse_file_info {
  int flags;
  uint64_t fh;
  int writepage;
  unsigned int direct_io     : 1;
  unsigned int keep_cache    : 1;
  unsigned int flush         : 1;
  unsigned int nonseekable   : 1;
  unsigned int flock_release : 1;
  unsigned int cache_readdir : 1;
  uint32_t padding;
  uint64_t lock_owner;
  uint32_t poll_events;
};

// Entry parameters for replies
struct fuse_entry_param {
  fuse_ino_t ino;
  unsigned long generation;
  struct stat attr;
  double attr_timeout;
  double entry_timeout;
};

// Directory entry structure
struct fuse_dirent {
  fuse_ino_t ino;
  off_t off;
  uint32_t namelen;
  uint32_t type;
  char name[];
};

// Connection info
struct fuse_conn_info {
  unsigned proto_major;
  unsigned proto_minor;
  unsigned async_read;
  unsigned max_write;
  unsigned max_readahead;
  unsigned capable;
  unsigned want;
  uint32_t max_background;
  uint32_t congestion_threshold;
  uint32_t time_gran;
  uint32_t reserved[22];
  unsigned no_interrupt : 1;
  unsigned reserved2    : 31;
};

// Arguments structure
struct fuse_args {
  int argc;
  char **argv;
  int allocated;
};

// Command line options structure
struct fuse_cmdline_opts {
  int singlethread;
  int foreground;
  int debug;
  int nodefault_subtype;
  char *mountpoint;
  int show_version;
  int show_help;
  int clone_fd;
  unsigned int max_idle_threads;
  unsigned int max_threads;  // Added missing field
};

// Loop configuration structure
struct fuse_loop_config {
  int clone_fd;
  unsigned int max_idle_threads;
  unsigned int max_threads;
};

// Session structure (opaque)
struct fuse_session;

// Lowlevel operations structure
struct fuse_lowlevel_ops {
  void (*init)(void *userdata, struct fuse_conn_info *conn);
  void (*destroy)(void *userdata);
  void (*lookup)(fuse_req_t req, fuse_ino_t parent, const char *name);
  void (*forget)(fuse_req_t req, fuse_ino_t ino, uint64_t nlookup);
  void (*getattr)(fuse_req_t req, fuse_ino_t ino, struct fuse_file_info *fi);
  void (*setattr)(fuse_req_t req, fuse_ino_t ino, struct stat *attr, int to_set,
                  struct fuse_file_info *fi);
  void (*readlink)(fuse_req_t req, fuse_ino_t ino);
  void (*mknod)(fuse_req_t req, fuse_ino_t parent, const char *name,
                mode_t mode, dev_t rdev);
  void (*mkdir)(fuse_req_t req, fuse_ino_t parent, const char *name,
                mode_t mode);
  void (*unlink)(fuse_req_t req, fuse_ino_t parent, const char *name);
  void (*rmdir)(fuse_req_t req, fuse_ino_t parent, const char *name);
  void (*symlink)(fuse_req_t req, const char *link, fuse_ino_t parent,
                  const char *name);
  void (*rename)(fuse_req_t req, fuse_ino_t parent, const char *name,
                 fuse_ino_t newparent, const char *newname, unsigned int flags);
  void (*link)(fuse_req_t req, fuse_ino_t ino, fuse_ino_t newparent,
               const char *newname);
  void (*open)(fuse_req_t req, fuse_ino_t ino, struct fuse_file_info *fi);
  void (*read)(fuse_req_t req, fuse_ino_t ino, size_t size, off_t off,
               struct fuse_file_info *fi);
  void (*write)(fuse_req_t req, fuse_ino_t ino, const char *buf, size_t size,
                off_t off, struct fuse_file_info *fi);
  void (*flush)(fuse_req_t req, fuse_ino_t ino, struct fuse_file_info *fi);
  void (*release)(fuse_req_t req, fuse_ino_t ino, struct fuse_file_info *fi);
  void (*fsync)(fuse_req_t req, fuse_ino_t ino, int datasync,
                struct fuse_file_info *fi);
  void (*opendir)(fuse_req_t req, fuse_ino_t ino, struct fuse_file_info *fi);
  void (*readdir)(fuse_req_t req, fuse_ino_t ino, size_t size, off_t off,
                  struct fuse_file_info *fi);
  void (*releasedir)(fuse_req_t req, fuse_ino_t ino, struct fuse_file_info *fi);
  void (*fsyncdir)(fuse_req_t req, fuse_ino_t ino, int datasync,
                   struct fuse_file_info *fi);
  void (*statfs)(fuse_req_t req, fuse_ino_t ino);
  void (*setxattr)(fuse_req_t req, fuse_ino_t ino, const char *name,
                   const char *value, size_t size, int flags);
  void (*getxattr)(fuse_req_t req, fuse_ino_t ino, const char *name,
                   size_t size);
  void (*listxattr)(fuse_req_t req, fuse_ino_t ino, size_t size);
  void (*removexattr)(fuse_req_t req, fuse_ino_t ino, const char *name);
  void (*access)(fuse_req_t req, fuse_ino_t ino, int mask);
  void (*create)(fuse_req_t req, fuse_ino_t parent, const char *name,
                 mode_t mode, struct fuse_file_info *fi);
  void (*getlk)(fuse_req_t req, fuse_ino_t ino, struct fuse_file_info *fi,
                struct flock *lock);
  void (*setlk)(fuse_req_t req, fuse_ino_t ino, struct fuse_file_info *fi,
                struct flock *lock, int sleep);
  void (*bmap)(fuse_req_t req, fuse_ino_t ino, size_t blocksize, uint64_t idx);
  void (*ioctl)(fuse_req_t req, fuse_ino_t ino, unsigned int cmd, void *arg,
                struct fuse_file_info *fi, unsigned flags, const void *in_buf,
                size_t in_bufsz, size_t out_bufsz);
  void (*poll)(fuse_req_t req, fuse_ino_t ino, struct fuse_file_info *fi,
               struct fuse_pollhandle *ph);
  void (*write_buf)(fuse_req_t req, fuse_ino_t ino, struct fuse_bufvec *bufv,
                    off_t off, struct fuse_file_info *fi);
  void (*retrieve_reply)(fuse_req_t req, void *cookie, fuse_ino_t ino,
                         off_t offset, struct fuse_bufvec *bufv);
  void (*forget_multi)(fuse_req_t req, size_t count,
                       struct fuse_forget_data *forgets);
  void (*flock)(fuse_req_t req, fuse_ino_t ino, struct fuse_file_info *fi,
                int op);
  void (*fallocate)(fuse_req_t req, fuse_ino_t ino, int mode, off_t offset,
                    off_t length, struct fuse_file_info *fi);
  void (*readdirplus)(fuse_req_t req, fuse_ino_t ino, size_t size, off_t off,
                      struct fuse_file_info *fi);
  void (*copy_file_range)(fuse_req_t req, fuse_ino_t ino_in, off_t off_in,
                          struct fuse_file_info *fi_in, fuse_ino_t ino_out,
                          off_t off_out, struct fuse_file_info *fi_out,
                          size_t len, int flags);
  void (*lseek)(fuse_req_t req, fuse_ino_t ino, off_t off, int whence,
                struct fuse_file_info *fi);
};

// Mock request structure
struct fuse_req {
  const struct fuse_lowlevel_ops *ops;
  void *userdata;
  int request_id;
};

// Function declarations
int fuse_reply_err(fuse_req_t req, int err);
int fuse_reply_entry(fuse_req_t req, const struct fuse_entry_param *e);
int fuse_reply_open(fuse_req_t req, const struct fuse_file_info *fi);
int fuse_reply_attr(fuse_req_t req, const struct stat *attr,
                    double attr_timeout);
int fuse_reply_readdir(fuse_req_t req, const char *buf, size_t size);
int fuse_reply_buf(fuse_req_t req, const char *buf, size_t size);
size_t fuse_add_direntry(fuse_req_t req, char *buf, size_t bufsize,
                         const char *name, const struct stat *stbuf, off_t off);
int fuse_main_mt(int argc, char *argv[], const struct fuse_lowlevel_ops *op,
                 void *userdata);
void *fuse_req_userdata(fuse_req_t req);

// Command line parsing functions
int fuse_parse_cmdline(struct fuse_args *args, struct fuse_cmdline_opts *opts);
void fuse_cmdline_help(void);
void fuse_lowlevel_help(void);
void fuse_lowlevel_version(void);

// Session management functions
struct fuse_session *fuse_session_new(struct fuse_args *args,
                                      const struct fuse_lowlevel_ops *op,
                                      size_t op_size, void *userdata);
void fuse_session_destroy(struct fuse_session *se);
int fuse_session_mount(struct fuse_session *se, char *mountpoint);
void fuse_session_unmount(struct fuse_session *se);
int fuse_session_fd(struct fuse_session *se);
void fuse_session_exit(struct fuse_session *se);
int fuse_session_exited(struct fuse_session *se);

// Loop configuration functions
struct fuse_loop_config *fuse_loop_cfg_create(void);
void fuse_loop_cfg_destroy(struct fuse_loop_config *config);
void fuse_loop_cfg_set_clone_fd(struct fuse_loop_config *config, int clone_fd);
void fuse_loop_cfg_set_max_threads(struct fuse_loop_config *config,
                                   unsigned int max_threads);
void fuse_loop_cfg_set_idle_threads(struct fuse_loop_config *config,
                                    unsigned int idle_threads);

// Loop functions
int fuse_session_loop(struct fuse_session *se);
int fuse_session_loop_mt(struct fuse_session *se,
                         struct fuse_loop_config *config);

// Argument functions
void fuse_opt_free_args(struct fuse_args *args);

// Version and signal handling functions
const char *fuse_pkgversion(void);
int fuse_set_signal_handlers(struct fuse_session *se);
void fuse_remove_signal_handlers(struct fuse_session *se);
int fuse_daemonize(int foreground);

// FUSE_ARGS_INIT macro
#define FUSE_ARGS_INIT(argc, argv) {argc, argv, 0}

#define FUSE_ROOT_ID 1

// Helper functions for unit testing
void mock_fuse_call_release(const struct fuse_lowlevel_ops *ops, void *userdata,
                           fuse_ino_t ino, struct fuse_file_info *fi);

#endif  // MOCK_FUSE_LOWLEVEL_H
