/**
 * This file is part of the CernVM File System.
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>

#include <sys/stat.h>
#include <unistd.h>

// Include the mock fuse headers first to avoid conflicts
#include "../mockfuse/mock_fuse_lowlevel.h"


// Forward declarations to avoid including conflicting headers
class FileSystem;
class MountPoint;
class CacheManager;
class ChunkTables;

namespace catalog {
class ClientCatalogManager;
}

namespace glue {
class PageCacheTracker;
}

namespace perf {
class Counter;
class Statistics;
}

namespace cvmfs {
// Define the global variables for testing
FileSystem *file_system_ = nullptr;
MountPoint *mount_point_ = nullptr;
}

namespace loader {
struct CvmfsExports {
  uint32_t version;
  uint32_t size;
  std::string so_version;
  // Function pointers
  int (*fnAltProcessFlavor)(int argc, char **argv);
  int (*fnInit)(const void *loader_exports);
  void (*fnSpawn)();
  void (*fnFini)();
  std::string (*fnGetErrorMsg)();
  bool (*fnMaintenanceMode)(const int fd_progress);
  bool (*fnSaveState)(const int fd_progress, void *saved_states);
  bool (*fnRestoreState)(const int fd_progress, const void *saved_states);
  void (*fnFreeSavedState)(const int fd_progress, const void *saved_states);
  struct fuse_lowlevel_ops cvmfs_operations;
};

// Define the global variable for testing
CvmfsExports *g_cvmfs_exports = nullptr;
}

// Create a global alias for easier access in tests
using loader::g_cvmfs_exports;

// Include necessary headers without conflicts
#include "file_chunk.h"
#include "glue_buffer.h"
#include "statistics.h"
#include "util/logging.h"
#include "smallhash.h"
#include "util/murmur.hxx"

using ::testing::_;
using ::testing::Return;
using ::testing::StrictMock;
using ::testing::NiceMock;
using ::testing::InSequence;

// Helper function for unit testing - allows direct invocation of FUSE callbacks
void mock_fuse_call_release(const struct fuse_lowlevel_ops *ops, void *userdata,
                           fuse_ino_t ino, struct fuse_file_info *fi) {
  if (ops && ops->release) {
    struct fuse_req mock_req = {
        .ops = ops, .userdata = userdata, .request_id = 997};
    ops->release(&mock_req, ino, fi);
  }
}

namespace cvmfs {
// Global variables are already defined above
}  // namespace cvmfs

// Forward declare the cvmfs_release function
extern "C" {
}

// Hash function for uint64_t
static uint32_t hasher_uint64t(const uint64_t &key) {
  return MurmurHash2(&key, sizeof(key), 0x07387a4f);
}

// Mock classes for testing cvmfs_release
class MockCatalogManager {
 public:
  MockCatalogManager() {}
  MOCK_CONST_METHOD1(MangleInode, fuse_ino_t(fuse_ino_t ino));
};

class MockPageCacheTracker {
 public:
  MockPageCacheTracker() {}
  MOCK_METHOD1(Close, void(uint64_t inode));
};

class MockChunkTables {
 public:
  MockChunkTables() {
    // Initialize the hash maps to avoid crashes
    handle2uniqino.Init(16, 0, hasher_uint64t);
    handle2fd.Init(16, 0, hasher_uint64t);
    inode2chunks.Init(16, 0, hasher_uint64t);
    inode2references.Init(16, 0, hasher_uint64t);
  }

  MOCK_METHOD0(Lock, void());
  MOCK_METHOD0(Unlock, void());

  // Expose the hash maps for direct manipulation in tests
  SmallHashDynamic<uint64_t, uint64_t> handle2uniqino;
  SmallHashDynamic<uint64_t, ChunkFd> handle2fd;
  SmallHashDynamic<uint64_t, FileChunkReflist> inode2chunks;
  SmallHashDynamic<uint64_t, uint32_t> inode2references;
};

class MockCacheManager {
 public:
  MockCacheManager() {}
  MOCK_METHOD1(Close, int(int fd));
};

class MockFileSystem {
 public:
  MockFileSystem() {}
  MOCK_METHOD0(cache_mgr, CacheManager*());
  MOCK_METHOD0(no_open_files, perf::Counter*());
  MOCK_METHOD0(hist_fs_release, perf::Statistics*());
};

class MockMountPoint {
 public:
  MockMountPoint() {}
  MOCK_METHOD0(catalog_mgr, catalog::ClientCatalogManager*());
  MOCK_METHOD0(page_cache_tracker, glue::PageCacheTracker*());
  MOCK_METHOD0(chunk_tables, ChunkTables*());
};

// Test fixture for cvmfs_release tests
class T_CvmfsRelease : public ::testing::Test {
 protected:
  virtual void SetUp() {
    // Initialize logging to suppress output during tests
    SetLogVerbosity(kLogNone);

    // Create mock objects
    mock_file_system_ = new NiceMock<MockFileSystem>();
    mock_mount_point_ = new NiceMock<MockMountPoint>();
    mock_catalog_mgr_ = new NiceMock<MockCatalogManager>();
    mock_page_cache_tracker_ = new NiceMock<MockPageCacheTracker>();
    mock_chunk_tables_ = new NiceMock<MockChunkTables>();
    mock_cache_mgr_ = new NiceMock<MockCacheManager>();
    mock_statistics_ = new perf::Statistics();
    mock_counter_ = mock_statistics_->Register("test_counter", "Test counter");

    // Set up default return values for mock objects
    ON_CALL(*mock_mount_point_, catalog_mgr())
        .WillByDefault(Return(reinterpret_cast<catalog::ClientCatalogManager*>(mock_catalog_mgr_)));
    ON_CALL(*mock_mount_point_, page_cache_tracker())
        .WillByDefault(Return(reinterpret_cast<glue::PageCacheTracker*>(mock_page_cache_tracker_)));
    ON_CALL(*mock_mount_point_, chunk_tables())
        .WillByDefault(Return(reinterpret_cast<ChunkTables*>(mock_chunk_tables_)));
    ON_CALL(*mock_file_system_, cache_mgr())
        .WillByDefault(Return(reinterpret_cast<CacheManager*>(mock_cache_mgr_)));
    ON_CALL(*mock_file_system_, no_open_files())
        .WillByDefault(Return(mock_counter_));
    ON_CALL(*mock_file_system_, hist_fs_release())
        .WillByDefault(Return(mock_statistics_));

    // MangleInode should be a noop as requested
    ON_CALL(*mock_catalog_mgr_, MangleInode(_))
        .WillByDefault([](fuse_ino_t ino) { return ino; });

    // Set global pointers
    cvmfs::file_system_ = reinterpret_cast<FileSystem*>(mock_file_system_);
    cvmfs::mount_point_ = reinterpret_cast<MountPoint*>(mock_mount_point_);

    // Initialize g_cvmfs_exports for testing
    loader::g_cvmfs_exports = new loader::CvmfsExports();
    loader::g_cvmfs_exports->cvmfs_operations.release = [](fuse_req_t req, fuse_ino_t ino, struct fuse_file_info *fi) {
      // This is a mock implementation that calls the actual cvmfs_release logic
      // In a real test, this would call the actual cvmfs_release function
    };

    // Initialize mock request
    mock_req_.ops = nullptr;
    mock_req_.userdata = nullptr;
    mock_req_.request_id = 1;
  }

  virtual void TearDown() {
    // Clean up global pointers
    cvmfs::file_system_ = nullptr;
    cvmfs::mount_point_ = nullptr;

    // Clean up g_cvmfs_exports
    delete loader::g_cvmfs_exports;
    loader::g_cvmfs_exports = nullptr;

    // Clean up mock objects
    delete mock_statistics_;
    delete mock_cache_mgr_;
    delete mock_chunk_tables_;
    delete mock_page_cache_tracker_;
    delete mock_catalog_mgr_;
    delete mock_mount_point_;
    delete mock_file_system_;
  }

 protected:
  NiceMock<MockFileSystem>* mock_file_system_;
  NiceMock<MockMountPoint>* mock_mount_point_;
  NiceMock<MockCatalogManager>* mock_catalog_mgr_;
  NiceMock<MockPageCacheTracker>* mock_page_cache_tracker_;
  NiceMock<MockChunkTables>* mock_chunk_tables_;
  NiceMock<MockCacheManager>* mock_cache_mgr_;
  perf::Statistics* mock_statistics_;
  perf::Counter* mock_counter_;

  struct fuse_req mock_req_;
};

// Test cases for cvmfs_release
TEST_F(T_CvmfsRelease, ReleaseNormalFile) {
  // Test releasing a normal file (not chunked)
  fuse_ino_t test_ino = 123;
  struct fuse_file_info fi = {0};
  fi.fh = 456;  // file handle

  // Set up expectations
  EXPECT_CALL(*mock_catalog_mgr_, MangleInode(test_ino))
      .WillOnce(Return(test_ino));  // noop as requested

  EXPECT_CALL(*mock_page_cache_tracker_, Close(test_ino))
      .Times(1);  // Should be called once

  EXPECT_CALL(*mock_chunk_tables_, Lock())
      .Times(1);
  EXPECT_CALL(*mock_chunk_tables_, Unlock())
      .Times(1);

  // Set up chunk tables to return false for handle lookup (not a chunked file)
  mock_chunk_tables_->handle2uniqino.Insert(fi.fh, test_ino);

  // Get the cvmfs operations
  loader::CvmfsExports *exports = g_cvmfs_exports;
  ASSERT_NE(exports, nullptr);
  ASSERT_NE(exports->cvmfs_operations.release, nullptr);

  // Call cvmfs_release through the mock fuse interface
  mock_fuse_call_release(&exports->cvmfs_operations, nullptr,
                        test_ino, &fi);
}

TEST_F(T_CvmfsRelease, ReleaseChunkedFile) {
  // Test releasing a chunked file
  fuse_ino_t test_ino = 789;
  struct fuse_file_info fi = {0};
  fi.fh = 101112;  // file handle

  // Set up a chunked file scenario
  ChunkFd chunk_fd;
  chunk_fd.fd = 42;
  FileChunkReflist chunks;

  // Set up expectations
  EXPECT_CALL(*mock_catalog_mgr_, MangleInode(test_ino))
      .WillOnce(Return(test_ino));

  EXPECT_CALL(*mock_page_cache_tracker_, Close(test_ino))
      .Times(1);

  EXPECT_CALL(*mock_chunk_tables_, Lock())
      .Times(1);
  EXPECT_CALL(*mock_chunk_tables_, Unlock())
      .Times(1);

  EXPECT_CALL(*mock_cache_mgr_, Close(chunk_fd.fd))
      .WillOnce(Return(0));  // Successful close

  // Set up chunk tables for a chunked file
  mock_chunk_tables_->handle2uniqino.Insert(fi.fh, test_ino);
  mock_chunk_tables_->handle2fd.Insert(fi.fh, chunk_fd);
  mock_chunk_tables_->inode2chunks.Insert(test_ino, chunks);
  mock_chunk_tables_->inode2references.Insert(test_ino, 1);

  // Get the cvmfs operations
  loader::CvmfsExports *exports = g_cvmfs_exports;
  ASSERT_NE(exports, nullptr);
  ASSERT_NE(exports->cvmfs_operations.release, nullptr);

  // Call cvmfs_release through the mock fuse interface
  mock_fuse_call_release(&exports->cvmfs_operations, nullptr,
                        test_ino, &fi);
}

TEST_F(T_CvmfsRelease, ReleaseFileWithMultipleReferences) {
  // Test releasing a file that has multiple references
  fuse_ino_t test_ino = 555;
  struct fuse_file_info fi = {0};
  fi.fh = 666;

  // Set up expectations
  EXPECT_CALL(*mock_catalog_mgr_, MangleInode(test_ino))
      .WillOnce(Return(test_ino));

  EXPECT_CALL(*mock_page_cache_tracker_, Close(test_ino))
      .Times(1);

  EXPECT_CALL(*mock_chunk_tables_, Lock())
      .Times(1);
  EXPECT_CALL(*mock_chunk_tables_, Unlock())
      .Times(1);

  // Set up chunk tables with multiple references
  mock_chunk_tables_->handle2uniqino.Insert(fi.fh, test_ino);
  mock_chunk_tables_->inode2references.Insert(test_ino, 3);  // Multiple refs

  // Get the cvmfs operations
  loader::CvmfsExports *exports = g_cvmfs_exports;
  ASSERT_NE(exports, nullptr);
  ASSERT_NE(exports->cvmfs_operations.release, nullptr);

  // Call cvmfs_release through the mock fuse interface
  mock_fuse_call_release(&exports->cvmfs_operations, nullptr,
                        test_ino, &fi);
}
